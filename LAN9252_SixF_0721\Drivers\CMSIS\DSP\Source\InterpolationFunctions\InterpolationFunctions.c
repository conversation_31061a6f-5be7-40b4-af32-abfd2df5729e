/* ----------------------------------------------------------------------
 * Project:      CMSIS DSP Library
 * Title:        InterpolationFunctions.c
 * Description:  Combination of all interpolation function source files.
 *
 * $Date:        22. July 2020
 * $Revision:    V1.0.0
 *
 * Target Processor: Cortex-M cores
 * -------------------------------------------------------------------- */
/*
 * Copyright (C) 2020 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "arm_bilinear_interp_f32.c"
#include "arm_bilinear_interp_q15.c"
#include "arm_bilinear_interp_q31.c"
#include "arm_bilinear_interp_q7.c"
#include "arm_linear_interp_f32.c"
#include "arm_linear_interp_q15.c"
#include "arm_linear_interp_q31.c"
#include "arm_linear_interp_q7.c"
#include "arm_spline_interp_f32.c"
#include "arm_spline_interp_init_f32.c"



